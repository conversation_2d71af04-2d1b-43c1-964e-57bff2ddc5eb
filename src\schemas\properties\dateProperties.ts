import { z } from "zod";

export const datePropertiesSchema = z
  .object({
    dateFormat: z.string().min(1, { message: "Date format is required" }),
    allowableDate: z.string().min(1, { message: "Allowable Date is required" }),
    minimumDateRange: z.string().optional(),
    maximumDateRange: z.string().optional(),
  })
  .superRefine(({ allowableDate, minimumDateRange, maximumDateRange }, ctx) => {
    if (allowableDate === "Date Range") {
      if (!minimumDateRange) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum date range is required",
          path: ["minimumDateRange"],
        });
      }

      if (!maximumDateRange) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Maximum date range is required",
          path: ["maximumDateRange"],
        });
      }

      if (minimumDateRange && maximumDateRange && new Date(minimumDateRange) >= new Date(maximumDateRange)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum date must be before maximum date",
          path: ["minimumDateRange"],
        });
      }
    }
  });
